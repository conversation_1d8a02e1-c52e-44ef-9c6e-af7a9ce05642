---
name: Bug report
about: Create a report to help us improve
title: "# 问题标题：简洁且具有描述性的问题标题"
labels: bug
assignees: JaveleyQAQ

---
# 🙋提问智力得分表
| 智力得分|提问方式 
| -------| ------ 
| 100分  | 我在xxx遇到了一个问题，在翻阅文档和视频后，这里是报错截图，我参考了百度或谷歌中xxx文章进行解决，但仍不能解决! 
| 90分   | 我遇到了xxx问题，我翻阅了文档和视频，没有找到解决方案，完整的报错截图如下。。。。。。
| 60分   | 这里是完整的报错截图，请问应该怎么解决
| 10分   | 这咋回事啊(手机拍屏幕)
| 0分    |  这怎么办啊(不完整的报错截图)
| sb    |  大神求带、这怎么不能用了我打不开了、你们这个正常吗、文件在哪啊、怎么学啊我是小白能帮我吗


## 问题描述
- 请在这里提供问题的详细描述，包括您遇到的具体问题和任何相关的背景信息。

## 环境信息
- **操作系统及版本**：例如 Windows 10, macOS Big Sur 11.1
- **编程语言和版本**：例如 Python 3.8.5
- **相关库或框架及版本**：例如 Django 3.1.7
- **GitHub 仓库名称和问题相关的代码分支**：如果有的话，提供仓库链接和分支信息。

## 复现步骤
1. 步骤一：描述第一步操作。
2. 步骤二：描述第二步操作。
3. 以此类推，直到步骤 N。

## 预期结果
- 描述您期望的结果或行为。

## 实际结果
- 描述实际发生的结果或行为。
- 如果有错误信息，请提供完整的错误日志或截图。

## 其他信息
- 提供任何可能有助于解决问题的其他信息，例如您尝试过的解决方案或相关工作的链接。

## 附加文件
- 如果需要附加文件，请确保它们是必要的，并且不会泄露任何敏感信息。可以使用 GitHub 的上传功能或提供文件的下载链接。

---

请在提交问题之前，检查您的描述是否清晰、准确，并且提供了足够的信息以便他人理解您的问题。感谢您的合作！
