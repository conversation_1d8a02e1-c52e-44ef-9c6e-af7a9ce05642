# Publish package on release branch if it's tagged with 'v*'

name: build & release

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branch: [main, master]
    tags:
      - 'v*'

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  release:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-versions: ['3.9']

    # map step outputs to job outputs so they can be share among jobs
    outputs:
      package_version: ${{ steps.variables_step.outputs.package_version }}
      package_name: ${{ steps.variables_step.outputs.package_name }}
      repo_name: ${{ steps.variables_step.outputs.repo_name }}
      repo_owner: ${{ steps.variables_step.outputs.repo_owner }}

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4

      - name: build change log
        id: build_changelog
        uses: mikepenz/release-changelog-builder-action@v3.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-versions }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install tox-gh-actions poetry

        # declare package_version, repo_owner, repo_name, package_name so you may use it in web hooks.
      - name: Declare variables for convenient use
        id: variables_step
        run: |
          echo "repo_owner=${GITHUB_REPOSITORY%/*}" >> $GITHUB_OUTPUT
          echo "repo_name=${GITHUB_REPOSITORY#*/}" >> $GITHUB_OUTPUT
          echo "package_name=`poetry version | awk '{print $1}'`" >> $GITHUB_OUTPUT
          echo "package_version=`poetry version --short`" >> $GITHUB_OUTPUT
        shell: bash

      - name: publish documentation
        run: |
          poetry install -E doc
          mkdocs build
          git config --global user.name Docs deploy
          git config --global user.email <EMAIL>
          mike deploy -p -f --ignore `poetry version --short` latest
          mike set-default -p `poetry version --short`

      - name: Build wheels and source tarball
        run: |
          poetry lock
          poetry build

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: Release ${{ github.ref_name }}
          body: ${{ steps.build_changelog.outputs.changelog }}
          draft: false
          prerelease: false

      - name: publish to PYPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          skip_existing: true

  notification:
    needs: release
    if: always()
    runs-on: ubuntu-latest
    steps:
      - uses: martialonline/workflow-status@v2
        id: check

      - name: build success notification via email
        if: ${{ steps.check.outputs.status == 'success' }}
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.BUILD_NOTIFY_MAIL_SERVER }}
          server_port: ${{ secrets.BUILD_NOTIFY_MAIL_PORT }}
          username: ${{ secrets.BUILD_NOTIFY_MAIL_FROM }}
          password: ${{ secrets.BUILD_NOTIFY_MAIL_PASSWORD }}
          from: build-bot
          to: ${{ secrets.BUILD_NOTIFY_MAIL_RCPT }}
          subject: ${{ needs.release.outputs.package_name }}.${{ needs.release.outputs.package_version}} build successfully
          convert_markdown: true
          html_body: |
            ## Build Success
            ${{ needs.release.outputs.package_name }}.${{ needs.release.outputs.package_version }} has been published to PYPI

            ## Change Details
            ${{ github.event.head_commit.message }}

            For more information, please check change history at https://${{ needs.release.outputs.repo_owner }}.github.io/${{ needs.release.outputs.repo_name }}/${{ needs.release.outputs.package_version }}/history

            ## Package Download
            The pacakge is available at: https://pypi.org/project/${{ needs.release.outputs.package_name }}/

      - name: build failure notification via email
        if: ${{ steps.check.outputs.status == 'failure' }}
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.BUILD_NOTIFY_MAIL_SERVER }}
          server_port: ${{ secrets.BUILD_NOTIFY_MAIL_PORT }}
          username: ${{ secrets.BUILD_NOTIFY_MAIL_FROM }}
          password: ${{ secrets.BUILD_NOTIFY_MAIL_PASSWORD }}
          from: build-bot
          to: ${{ secrets.BUILD_NOTIFY_MAIL_RCPT }}
          subject: ${{ needs.release.outputs.package_name }}.${{ needs.release.outputs.package_version}} build failure
          convert_markdown: true
          html_body: |
            ## Change Details
            ${{ github.event.head_commit.message }}

            ## Status: ${{ steps.check.outputs.status }}


            ## View Log
            https://github.com/${{ needs.release.outputs.repo_owner }}/${{ needs.release.outputs.repo_name }}/actions


      # - name: Dingtalk Robot Notify
      #   if: always()
      #   uses: leafney/dingtalk-action@v1.0.0
      #   env:
      #     DINGTALK_ACCESS_TOKEN: ${{ secrets.DINGTALK_ACCESS_TOKEN }}
      #     DINGTALK_SECRET: ${{ secrets.DINGTALK_SECRET }}
      #   with:
      #     msgtype: markdown
      #     title: CI Notification | Success
      #     text: |
      #       ### ${{ needs.release.outputs.package_name }} Build Success
      #       ${{ needs.release.outputs.package_version }} has been published to PYPI
      #       ### Change History
      #       Please check change history at https://${{ needs.release.outputs.repo_owner }}.github.io/${{ needs.release.outputs.repo_name }}/latest/history
      #       ### Package Download
      #       Please download the pacakge at: https://pypi.org/project/${{ needs.release.outputs.repo_name }}/
